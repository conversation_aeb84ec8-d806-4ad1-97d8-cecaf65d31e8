<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2') ?>">Attendance V2</a></li>
  <li>Day-wise Attendance Report</li>
</ul>

<div class="container-fluid">
  <div class="card cd_border">

    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('attendance_day_v2/Attendance_day_v2'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a>
            Day-wise Attendance Report

          </h3>
        </div>
      </div>
    </div>
    
    <div class="card-body"> 
      <form method="post" id ="daywiseAttendanceForm">
        <div class="row g-3">
          <div class="col-md-2">
            <div class="form-group">
              <label for="sectionid">Section</label>
              <select multiple="" name="classsecID[]" id="sectionid" class="form-control select2" title ="ALL" multiple >
                <option value="">Select Section</option>
                <?php foreach ($class_section as $cls_section) { ?>
                  <option value="<?= $cls_section->classID.'_'.$cls_section->sectionID ?>">
                    <?= $cls_section->class_name . ' ' . $cls_section->section_name ?>
                  </option>
                <?php } ?>
              </select>
            </div>
          </div>
          
          <div class="col-md-2">
          <div class="form-group">
            <label>Date Range</label>
            <div id="reportrange" class="dtrange">  
              <span></span>  
              <input type="hidden" name="from_date" id="from_date">
              <input type="hidden" name="to_date"  id="to_date">
            </div>
            </div>
          </div>
          
          <div class="col-md-2 my-5">
            <button type="submit" class="btn btn-primary w-100" id="submitBtn" onclick="daywiseAttendanceReport()" style="margin-top: -5px;">Get</button>
          </div>
        </div>
      </form>
      <div id ="daywise_report" class = ""></div>
    </div>
  </div>
</div>


<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script type="text/javascript">

$("#reportrange").daterangepicker({
    ranges: {
     'Today': [moment(), moment()],
     'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
     'Last 7 Days': [moment().subtract(6, 'days'), moment()],
      'Last 30 Days': [moment().subtract(29, 'days'), moment()],
     'This Month': [moment().startOf('month'), moment().endOf('month')],
      'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'MM.DD.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));

  const loading = `<div class="col-md-14 text-center d-flex justify-content-center align-items-center" style="text-align: center;">
                    <i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i>
                  </div>`;

function daywiseAttendanceReport() {
    $('#submitBtn').prop('disabled', true).text("Please wait...");
    $('#daywise_report').html(loading);

    let from_date = $('#from_date').val();
    let to_date = $('#to_date').val();
    let from_date_ymd = moment(from_date, 'DD-MM-YYYY').format('YYYY-MM-DD');
    let to_date_ymd = moment(to_date, 'DD-MM-YYYY').format('YYYY-MM-DD');
    let section_id = $('#sectionid').val();
    let current = new Date(from_date_ymd);
    let end = new Date(to_date_ymd);
    let all_dates = [];

    while (current <= end) {
        let formattedDate = current.toISOString().split('T')[0];
        all_dates.push(formattedDate);
        current.setDate(current.getDate() + 1);
    }

    $.ajax({
        url: "<?php echo site_url('attendance_day_v2/Attendance_day_v2/dayReport'); ?>",
        type: "POST",
        data: {
            classsecID: section_id,
            from_date: from_date_ymd,
            to_date: to_date_ymd
        },
        success: function(data) {
            try {
                data = JSON.parse(data);
                
                console.log("Response data:", data);

                if (data.details && Object.keys(data.details).length > 0) {
                    let holidays = (data.holidays && data.holidays.dates) ? data.holidays.dates : [];
                    let details = data.details;
                    
                    // Process student data
                    let student_data = {};
                    
                    // First pass: organize data by student
                    for (let date in details) {
                        if (details.hasOwnProperty(date)) {
                            let students = details[date];
                            for (let student of students) {
                                let id = student.admission_no;
                                if (!student_data[id]) {
                                    // Convert total_days to number if it's a string
                                    let totalDays = typeof student.total_days === 'string' ? 
                                        parseFloat(student.total_days) : student.total_days;
                                    
                                    student_data[id] = {
                                          std_name: student.std_name,
                                          admission_no: student.admission_no,
                                          roll_no: student.roll_no || '-',
                                          enrollment_number: student.enrollment_number || '-',
                                          sts_number: student.sts_number || '-',
                                          pen_number: student.pen_number || '-',
                                          csName: student.csName,
                                          attendance: {},
                                          present_days: 0,
                                          total_days: totalDays || 0
                                      };

                                }
                                
                                // Get session count or default
                                let sessionCount = student.session_count || 
                                    (student.total_days == 1 ? 1 : 2);
                                
                                // Store attendance data
                                let fmtDate = date;
                                student_data[id].attendance[fmtDate] = {
                                    morning: student.morning,
                                    afternoon: student.afternoon,
                                    present: student.present,
                                    session_count: sessionCount
                                };
                                
                                // Calculate present days (ensure present is a number)
                                let presentValue = typeof student.present === 'string' ?
                                    parseFloat(student.present) : student.present;
                                    
                                if (sessionCount == 1) {
                                    student_data[id].present_days += (student.morning == 1) ? 1 : 0;
                                } else {
                                    student_data[id].present_days += presentValue || 0;
                                }
                            }
                        }
                    }
                    
                    // Generate HTML table
                    let html = `<div class="scrolable-table-container" style="border: none;">
                        <table id="studentAttendanceTable" class="table table-bordered" style="margin-top:0rem">
                            <thead>
                                <tr>
                                    <th class="text-center" style="position:sticky;left:0;z-index:5;background:white;min-width:60px;width:60px;">Sl#</th>
                                    <th class="text-center" style="position:sticky;left:60px;z-index:5;background:white;min-width:180px;width:180px;text-align:left;white-space:normal;">Student Name</th>
                                    <th class="text-center">Admission No</th>
                                    <th class="text-center">Roll No</th>
                                    <th class="text-center">Enrollment No</th>
                                    <th class="text-center">Stats No</th>
                                    <th class="text-center">PEN No</th>
                                    <th class="text-center">Class</th>`;
                    
                    // Add date headers
                    for (let date of all_dates) {
                        let displayDate = moment(date).format('DD-MMM-YYYY');
                        let isHoliday = holidays.includes(date);
                        let dayOfWeek = moment(date).day(); // 0-6 (Sun-Sat)
                        let isWeekend = (dayOfWeek === 0 || dayOfWeek === 6);
                        let td_class = isHoliday ? 'table-holiday' : (isWeekend ? 'table-warning' : '');
                        
                        html += `<th class="text-center ${td_class}">${displayDate}</th>`;
                    }
                    
                    html += `
                                    <th class="text-center">Present Days</th>
                                    <th class="text-center">Total Days</th>
                                    <th class="text-center">Attendance %</th>
                                </tr>
                            </thead>
                            <tbody>`;
                    
                    // Add student rows
                    let sl = 1;
                    // console.log(student_data);
                    for (let id in student_data) {
                        if (student_data.hasOwnProperty(id)) {
                            let student = student_data[id];
                            // Ensure all numeric values are numbers before formatting
                            let presentDays = typeof student.present_days === 'number' ? student.present_days : parseFloat(student.present_days);
                            let totalDays = typeof student.total_days === 'number' ? student.total_days : parseFloat(student.total_days);
                            
                            let attendancePercent = (totalDays > 0) ? 
                                ((presentDays / totalDays) * 100).toFixed(1) + '%' : 'N/A';
                            
                            html += `
                                <tr>
                                    <td class="text-center" style="position:sticky;left:0;z-index:5;background:white;min-width:60px;width:60px;">${sl++}</td>
                                    <td style="position:sticky;left:60px;z-index:5;background:white;min-width:180px;width:180px;text-align:left;white-space:normal;">${student.std_name}</td>
                                    <td class="text-center">${student.admission_no}</td>
                                    <td class="text-center">${student.roll_no || '-'}</td>
                                    <td class="text-center">${student.enrollment_number || '-'}</td>
                                    <td class="text-center">${student.sts_number || '-'}</td>
                                    <td class="text-center">${student.pen_number || '-'}</td>
                                    <td>${student.csName}</td>`;
                            
                            // Add attendance cells for each date
                            for (let date of all_dates) {
                                let displayDate = moment(date).format('DD-MMM-YYYY');
                                let isHoliday = holidays.includes(date);
                                let dayOfWeek = moment(date).day();
                                let isWeekend = (dayOfWeek === 0 || dayOfWeek === 6);
                                
                                let status = '-';
                                let td_class = '';
                                
                                if (isHoliday) {
                                    status = 'H';
                                    td_class = 'table-holiday';
                                } else if (student.attendance[displayDate]) {                                  
                                  let att = student.attendance[displayDate];
                                  let sessions = att.session_count || (isWeekend ? 1 : 2);
                                  let morning = att.morning;
                                  let afternoon = att.afternoon;
                                  let present = att.present;
                                  
                                  if (sessions == 1) {
                                      // Single session day
                                      if (morning === null || morning === undefined || morning === '') {
                                            status = '-';
                                            td_class = 'table-light'; // Not taken
                                        } else if (morning == 1) {
                                            status = 'P';
                                            td_class = 'table-success';
                                        } else if (morning == 0) {
                                            status = 'A';
                                            td_class = 'table-danger';
                                        } else {
                                            status = '-';
                                            td_class = 'table-light';
                                        }
                                  } else {
                                      // Two session day
                                      if (morning === null && afternoon === null) {
                                          status = '-';
                                          td_class = 'table-light'; // Not taken
                                      } else if (present == 1) {
                                          status = 'P';
                                          td_class = 'table-success';
                                      } else if (present == 0.5) {
                                          status = 'H';
                                          if (morning == 1) status += '(M)';
                                          else if (afternoon == 1) status += '(A)';
                                          td_class = 'table-warning';
                                      } else if (present == 0) {
                                          status = 'A';
                                          td_class = 'table-danger';
                                      } else {
                                          status = '-';
                                          td_class = 'table-light';
                                      }
                                  }
                              } else {
                                    // No attendance data exists for this date
                                    status = '-';
                                    td_class = isWeekend ? 'table-warning' : 'table-light';
                                }
                                
                                html += `<td class="text-center ${td_class}">${status}</td>`;
                            }
                            
                            html += `
                                <td class="text-center">${presentDays.toFixed(1)}</td>
                                <td class="text-center">${totalDays.toFixed(1)}</td>
                                <td class="text-center">${attendancePercent}</td>
                            </tr>`;
                        }
                    }
                    
                    html += `</tbody></table></div>`;
                    
                    $('#daywise_report').html(html);
                 var table = $('#studentAttendanceTable').DataTable({
                  "language": {
                      "search": "",
                      "searchPlaceholder": "Enter Search..."
                  },
                  "paging": false,
                  "scrollX": true,
                  "scrollY": '60vh',
                  "autoWidth": true, // let DataTables handle column widths
                  "ordering": false,
                  "fixedHeader": true,
                  dom: 'lBfrtip',
                  buttons: [
                      {
                          extend: 'excelHtml5',
                          text: 'Excel',
                          filename: 'Daywise Attendance Report',
                          className: 'btn btn-info'
                      },
                      {
                          extend: 'print',
                          text: 'Print',
                          filename: 'Daywise Attendance Report',
                          className: 'btn btn-info'
                      }
                  ]
              });

             
                } else {
                    $('#daywise_report').html('<div class="no-data-display">No data found for the selected date range.</div>');
                }
            } catch (e) {
                console.error("Error processing data:", e);
                $('#daywise_report').html('<div class="no-data-display">Error processing data. Please try again.</div>');
            }
            $('#submitBtn').prop('disabled', false).text("Submit");
        },
        error: function(xhr, status, error) {
            console.error("AJAX Error:", error);
            $('#daywise_report').html('<div class="no-data-display">Error fetching data. Please try again.</div>');
            $('#submitBtn').prop('disabled', false).text("Submit");
        }
    });
}
$(document).ready(function() {
    $('.select2').select2({
        placeholder: "Select Section",
        allowClear: true,
        width: '100%'
    });
});
</script>
<style>
.form-group:last-child {
    margin-bottom: 12px;
}
.form-group
{
  margin: 8px 0px;
}
.btn-mag{
  margin-top: 21px;
}

@media screen and (max-width: 900px) {
  .btn-mag{
    margin-top: 8px;
  }
}

/* DataTables customization */
.dt-button {
  float: right;
  margin-left: 5px;
}
.dataTables_wrapper .dt-buttons {
  float: right;
  margin-bottom: 10px;
}
.dataTables_filter input {
  background-color: #f2f2f2;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-right: 5px;
  padding: 4px 8px;
}
.dataTables_wrapper .dataTables_filter {
  float: right;
  text-align: left;
  width: unset;
}


table.dataTable thead th,
table.dataTable tbody td {
    box-sizing: border-box;
    white-space: nowrap;
}

#daywise_report {
  width: 100%;
  overflow: hidden;
}
#daywise_report .scrolable-table-container {
  width: 100% !important;
  max-width: 100%;
  min-width: 0;
  margin: 0;
  position: relative;
  background: #fff;
  overflow: auto !important;
}
#daywise_report table {
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
}


.dataTables_scrollHeadInner table{
  margin-bottom: 0;
}

/* Custom holiday styling */
.table-holiday {
  background-color: #fff7e0 !important;
  color: #e1a100 !important;
  font-weight: 600;
}

</style>