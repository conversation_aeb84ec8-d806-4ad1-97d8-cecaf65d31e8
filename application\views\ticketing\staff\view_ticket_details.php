<!-- START BREADCRUMB -->
<ul class="breadcrumb">
  <li><a href="<?php echo site_url('avatars') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('ParentTicket_dashboard');?>">Parent Ticketing</a></li>
  <li><a  href="<?php echo site_url('parent_ticketing/view_assigned_tickets'); ?>">Parent Tickets</a></li>
  <li><a class="active">Ticket</a></li>
</ul>


<div class="col-md-12">
  <div class="panel panel-default new-panel-style_3">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="col-md-9 pl-0">
          <h3 class="card-title panel_title_new_style_staff"><a class="back_anchor" href="<?php echo site_url('parent_ticketing/view_assigned_tickets'); ?>"><span class="fa fa-arrow-left"></span></a>Ticket <?= $ticket->ticket_number ?> - <?= $ticket->title ?></h3>
        </div>
      </div>
    </div>
    <div class="col-md-12">
      <div class="card-body">
          <h3>Ticket Details</h3>
          <div class="row">
            <div class="col-md-6">
                <table  class="table table-bordered">
                    <thead>
                        <tr>
                            <th width="20%">Filled By</th>
                            <td width="20%"><?= $ticket->pName ?></td>
                        </tr>
                        <tr>
                            <th width="20%">Student</th>
                            <td width="20%"><?= $ticket->sName ?></td>
                        </tr>
                        <tr>
                            <th width="20%">Class</th>
                            <td width="20%"><?= $ticket->csName ?></td>
                        </tr>
                        <tr>
                            <th width="20%">Description</th>
                            <td width="20%"><?= $ticket->description ?></td>
                        </tr>
                    </thead>
                </table>
                <b>Comments :</b>
                <p><?php echo empty($ticket->display_comments)?'No Comments yet':$ticket->display_comments; ?> </p>
            </div>  
            <div class="col-md-6">
              <table  class="table table-bordered">
                  <thead>
                      <tr>
                          <th width="20%">Status</th>
                          <td width="20%"><font color="red"><?= $ticket->status ?></font></td>
                      </tr>
                      <tr>
                          <th width="20%">Filled On</th>
                          <td width="20%"><?= $ticket->filed_on ?></td>
                      </tr>
                      <tr>
                          <th width="20%">Assigned To:</th>
                          <td width="20%"><?= $ticket->assStaffName ?></td>
                      </tr>
                  </thead>
              </table>
              <br><br><p><b>Parent Attachments:</b><br>
              <?php if($ticket->attachments!='') { ?>
                      <span>
                          <?php foreach ($ticket->paths as $path) { ?>
                              <a class="btn btn-sm btn-primary mt-2" target="_blank" href="<?php echo $path['path']; ?>"><?php echo $path['name']; ?>&nbsp;<i class="fa fa-eye"></i></a>
                          <?php } ?>
                      </span><p>
                      <?php } else { ?>
                          <h5>No attachment</h5>
                      <br>
              <?php } ?>
              <p><b>Staff Attachments :</b>
              <?php if($ticket->staff_attacments != '') { ?>
                  <span>
                      <?php 
                      foreach($ticket->staff_attachments_paths as $staff_attacments) { ?>
                          <a class="btn btn-sm btn-primary mt-2" target="_blank" href ="<?php echo $staff_attacments['path'] ;?>"><?php echo $staff_attacments['name'];?>&nbsp;<i class="fa fa-eye"></i></a>
                      <?php } ?>
                  </span>
                  </p>
                  <?php } else { ?>
                      <h5>No attachment</h5>
              <?php  } ?>
            </div>
          </div>
      </div>
    </div>
  </div>
</div>

      <?php 
        if ($ticket->status !== 'Closed')
          $display_action = '';
        else 
          $display_action = 'none';  
      ?>

      

<div class="col-md-12">
  <div class="panel panel-default new-panel-style_3" style="display:<?= $display_action ?>;">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="col-md-9 pl-0">
          <h3 class="card-title panel_title_new_style_staff">Take Action</h3>
        </div>
      </div>
    </div>
          <form method="post" enctype="multipart/form-data" action="<?php echo site_url('parent_ticketing/submit_staff_response/'.$ticket->id);?>" data-parsley-validate="" id="editaction" >
            <div class="panel-body">
            <!--- Store the existing comments in a hidden field --->
            <input type="hidden" value="<?= $ticket->comments ?>" name="existing_comments">
              <div class="form-group">
                <label class="control-label" for="update_status">Status</label>
                <select class="form-control" name="update_status" id="update_status">
                  <?php
                    foreach ($status_flags as $status) {
                      $selected = ($status === $ticket->status)?'selected':'';
                      echo "<option value='$status' $selected>" . ucwords($status) . "</option>";
                    }
                  ?>
                </select>
              </div>
              <div class="form-group">
                <label class="control-label" for="assign_to_staff_id">Move To Other Staff</label>
                <select class="form-control" name="assign_to_staff_id" id="assign_to_staff_id">
                  <option value="-1">Select Staff</option>
                  <?php
                    foreach ($staff_list as $staff) {
                      // $selected = ($staff->id === $assigned_to)?'selected':'';
                      echo "<option value='$staff->id'>$staff->staffName</option>";
                    }
                  ?>
                </select>
              </div>
              <div class="form-group">
                <label class="control-label" for="assign_to_staff_id">Over Ride Category</label>
                 <select name="category" required="" id="category" class="form-control" required>
                            <option value="">Select Category</option>
                            <?php
                                foreach ($category_list as $cat) {
                                    $selected = $cat->is_default ? 'selected' : '';
                                    echo "<option value='$cat->id' $selected>$cat->name</option>";
                                }
                            ?>
                        </select>
              </div>

                
              <div class="form-group">
                <label class="control-label" for="comments">Comment</label>
                <textarea class="form-control" rows="5" id="comments" name="comments" ></textarea>
              </div>
              <div class="form-group">
                  <label class="control-label" for="exampleFormControlTextarea1" required="">Attachments</label>
                  <div class="">
                      <input type="file" name="staff_attachments[]" id="staff_attachments" multiple="">
                      <span class="help-block">You can upload multiple files</span>
                  </div>
              </div>

            <?php if ($ticket->status !== 'Closed') { ?>

            <div class="panel-footer">
              <div class="col-md-12">
                <center>
                  <button type="submit" id="buttonsubmit" class="btn btn-primary">Update / Closed</button>
                  <a class="btn btn-danger" href="<?php echo site_url('parent_ticketing/view_assigned_tickets'); ?>">Cancel</a>
                </center>
              </div>
            </div>
            <?php } ?>
          </div>

        </form>

      </div>
      </div>
    </div>
  </div>
</div>

<div class="visible-xs" style="padding:7px;">
  <a href="<?php echo site_url('parent_ticketing/view_assigned_tickets');?>" id="backBtn"><span class="fa fa-mail-reply"></span></a>
</div>


<script type="text/javascript">
var save_lock_button_clicked = false;
  $(document).ready(function(){
    $("#editaction").trackChanges();
    $(window).bind("beforeunload", function(event) {
      if ($("#editaction").isChanged() && !save_lock_button_clicked) {
        return 'Form data has changed';
      }
    });
  });

  $('#buttonsubmit').click(function(){
    if ($('#editaction').parsley().validate()) {
      $('#buttonsubmit').attr('disabled','disabled').html('Please wait..');
      $('#editaction').submit();
      save_lock_button_clicked = true;
    }
  });

</script>
